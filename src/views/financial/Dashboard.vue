<template>
  <div class="financial-dashboard">
    <!-- 筛选区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <!-- 第一行：统计日期 -->
        <div class="filter-row">
          <div class="filter-label">统计日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <!-- 第二行：统计机构 -->
        <div class="filter-row">
          <div class="filter-label">统计机构</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><component :is="BusinessOutlineIcon" /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="
                filterForm.selectedOrgs && filterForm.selectedOrgs.length > 0
              "
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 统计指标卡片 -->
    <n-grid :cols="4" :x-gap="16" :y-gap="16" class="stat-cards">
      <!-- 应收账款区域 -->
      <n-grid-item span="4">
        <n-card title="应收账款" size="small" class="custom-card">
          <n-grid :cols="4" :x-gap="16">
            <n-grid-item>
              <n-statistic
                label="已对账笔数"
                :value="statData.receivableConfirmed.cnt"
              />
            </n-grid-item>
            <n-grid-item>
              <div class="amount-statistic">
                <n-statistic
                  label="已对账金额"
                  :value="formatAmount(statData.receivableConfirmed.amount)"
                >
                  <template #suffix>
                    <span class="amount-in-wan">{{
                      formatAmountToWan(statData.receivableConfirmed.amount)
                    }}</span>
                  </template>
                </n-statistic>
                <div class="rate-indicators">
                  <div class="rate-indicator">
                    <span class="rate-label">同比</span>
                    <span
                      :style="{
                        color: getRateColor(
                          statData.receivableConfirmed.yoyRate
                        ),
                      }"
                    >
                      {{ formatRate(statData.receivableConfirmed.yoyRate) }}
                    </span>
                  </div>
                  <div class="rate-indicator">
                    <span class="rate-label">环比</span>
                    <span
                      :style="{
                        color: getRateColor(
                          statData.receivableConfirmed.momRate
                        ),
                      }"
                    >
                      {{ formatRate(statData.receivableConfirmed.momRate) }}
                    </span>
                  </div>
                </div>
              </div>
            </n-grid-item>
            <n-grid-item>
              <n-statistic
                label="未对账笔数"
                :value="statData.receivableUnConfirmed.cnt"
              />
            </n-grid-item>
            <n-grid-item>
              <div class="amount-statistic">
                <n-statistic
                  label="未对账金额"
                  :value="formatAmount(statData.receivableUnConfirmed.amount)"
                >
                  <template #suffix>
                    <span class="amount-in-wan">{{
                      formatAmountToWan(statData.receivableUnConfirmed.amount)
                    }}</span>
                  </template>
                </n-statistic>
                <div class="rate-indicators">
                  <div class="rate-indicator">
                    <span class="rate-label">同比</span>
                    <span
                      :style="{
                        color: getRateColor(
                          statData.receivableUnConfirmed.yoyRate
                        ),
                      }"
                    >
                      {{ formatRate(statData.receivableUnConfirmed.yoyRate) }}
                    </span>
                  </div>
                  <div class="rate-indicator">
                    <span class="rate-label">环比</span>
                    <span
                      :style="{
                        color: getRateColor(
                          statData.receivableUnConfirmed.momRate
                        ),
                      }"
                    >
                      {{ formatRate(statData.receivableUnConfirmed.momRate) }}
                    </span>
                  </div>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </n-card>
      </n-grid-item>

      <!-- 应付账款区域 -->
      <n-grid-item span="4">
        <n-card title="应付账款" size="small" class="custom-card">
          <n-grid :cols="4" :x-gap="16">
            <n-grid-item>
              <n-statistic
                label="已对账笔数"
                :value="statData.payableConfirmed.cnt"
              />
            </n-grid-item>
            <n-grid-item>
              <div class="amount-statistic">
                <n-statistic
                  label="已对账金额"
                  :value="formatAmount(statData.payableConfirmed.amount)"
                >
                  <template #suffix>
                    <span class="amount-in-wan">{{
                      formatAmountToWan(statData.payableConfirmed.amount)
                    }}</span>
                  </template>
                </n-statistic>
                <div class="rate-indicators">
                  <div class="rate-indicator">
                    <span class="rate-label">同比</span>
                    <span
                      :style="{
                        color: getRateColor(statData.payableConfirmed.yoyRate),
                      }"
                    >
                      {{ formatRate(statData.payableConfirmed.yoyRate) }}
                    </span>
                  </div>
                  <div class="rate-indicator">
                    <span class="rate-label">环比</span>
                    <span
                      :style="{
                        color: getRateColor(statData.payableConfirmed.momRate),
                      }"
                    >
                      {{ formatRate(statData.payableConfirmed.momRate) }}
                    </span>
                  </div>
                </div>
              </div>
            </n-grid-item>
            <n-grid-item>
              <n-statistic
                label="未对账笔数"
                :value="statData.payableUnConfirmed.cnt"
              />
            </n-grid-item>
            <n-grid-item>
              <div class="amount-statistic">
                <n-statistic
                  label="未对账金额"
                  :value="formatAmount(statData.payableUnConfirmed.amount)"
                >
                  <template #suffix>
                    <span class="amount-in-wan">{{
                      formatAmountToWan(statData.payableUnConfirmed.amount)
                    }}</span>
                  </template>
                </n-statistic>
                <div class="rate-indicators">
                  <div class="rate-indicator">
                    <span class="rate-label">同比</span>
                    <span
                      :style="{
                        color: getRateColor(
                          statData.payableUnConfirmed.yoyRate
                        ),
                      }"
                    >
                      {{ formatRate(statData.payableUnConfirmed.yoyRate) }}
                    </span>
                  </div>
                  <div class="rate-indicator">
                    <span class="rate-label">环比</span>
                    <span
                      :style="{
                        color: getRateColor(
                          statData.payableUnConfirmed.momRate
                        ),
                      }"
                    >
                      {{ formatRate(statData.payableUnConfirmed.momRate) }}
                    </span>
                  </div>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </n-card>
      </n-grid-item>

      <!-- 已收账款区域 -->
      <n-grid-item span="2">
        <n-card title="已收账款" size="small" class="custom-card">
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-statistic label="已收款笔数" :value="statData.recept.cnt" />
            </n-grid-item>
            <n-grid-item>
              <div class="amount-statistic">
                <n-statistic
                  label="已收款金额"
                  :value="formatAmount(statData.recept.amount)"
                >
                  <template #suffix>
                    <span class="amount-in-wan">{{
                      formatAmountToWan(statData.recept.amount)
                    }}</span>
                  </template>
                </n-statistic>
                <div class="rate-indicators">
                  <div class="rate-indicator">
                    <span class="rate-label">同比</span>
                    <span
                      :style="{ color: getRateColor(statData.recept.yoyRate) }"
                    >
                      {{ formatRate(statData.recept.yoyRate) }}
                    </span>
                  </div>
                  <div class="rate-indicator">
                    <span class="rate-label">环比</span>
                    <span
                      :style="{ color: getRateColor(statData.recept.momRate) }"
                    >
                      {{ formatRate(statData.recept.momRate) }}
                    </span>
                  </div>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </n-card>
      </n-grid-item>

      <!-- 已付账款区域 -->
      <n-grid-item span="2">
        <n-card title="已付账款" size="small" class="custom-card">
          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-statistic label="已付款笔数" :value="statData.payment.cnt" />
            </n-grid-item>
            <n-grid-item>
              <div class="amount-statistic">
                <n-statistic
                  label="已付款金额"
                  :value="formatAmount(statData.payment.amount)"
                >
                  <template #suffix>
                    <span class="amount-in-wan">{{
                      formatAmountToWan(statData.payment.amount)
                    }}</span>
                  </template>
                </n-statistic>
                <div class="rate-indicators">
                  <div class="rate-indicator">
                    <span class="rate-label">同比</span>
                    <span
                      :style="{ color: getRateColor(statData.payment.yoyRate) }"
                    >
                      {{ formatRate(statData.payment.yoyRate) }}
                    </span>
                  </div>
                  <div class="rate-indicator">
                    <span class="rate-label">环比</span>
                    <span
                      :style="{ color: getRateColor(statData.payment.momRate) }"
                    >
                      {{ formatRate(statData.payment.momRate) }}
                    </span>
                  </div>
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 图表区域 -->
    <n-grid :cols="2" :x-gap="16" :y-gap="16" class="chart-container">
      <!-- 机构维度-应收应付柱状图 -->
      <n-grid-item>
        <n-card title="机构应收应付" size="small" class="custom-card">
          <div ref="orgReceivablePayableChart" class="chart"></div>
        </n-card>
      </n-grid-item>

      <!-- 机构维度-收支总额柱状图 -->
      <n-grid-item>
        <n-card title="机构收支总额" size="small" class="custom-card">
          <div ref="orgIncomeExpenseChart" class="chart"></div>
        </n-card>
      </n-grid-item>

      <!-- 应收应付趋势图 -->
      <n-grid-item>
        <n-card title="应收应付趋势" size="small" class="custom-card">
          <template #header-extra>
            <n-button-group size="small">
              <n-button
                v-for="option in dateDimensionOptions"
                :key="option.value"
                :type="
                  filterForm.receivablePayableDateDimension === option.value
                    ? 'primary'
                    : 'default'
                "
                @click="
                  handleReceivablePayableDateDimensionChange(option.value)
                "
              >
                {{ option.label }}
              </n-button>
            </n-button-group>
          </template>
          <div ref="receivablePayableTrendChart" class="chart"></div>
        </n-card>
      </n-grid-item>

      <!-- 收款付款趋势图 -->
      <n-grid-item>
        <n-card title="收款付款趋势" size="small" class="custom-card">
          <template #header-extra>
            <n-button-group size="small">
              <n-button
                v-for="option in dateDimensionOptions"
                :key="option.value"
                :type="
                  filterForm.receptPaymentDateDimension === option.value
                    ? 'primary'
                    : 'default'
                "
                @click="handleReceptPaymentDateDimensionChange(option.value)"
              >
                {{ option.label }}
              </n-button>
            </n-button-group>
          </template>
          <div ref="receptPaymentTrendChart" class="chart"></div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择统计机构"
      :business-permission="null"
      :single="true"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive, computed } from "vue";
import { useMessage } from "naive-ui";
import * as echarts from "echarts";
import { financialApi } from "@/api/financial";
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
} from "@/utils/dateRange";
import { formatMoney } from "@/utils/money";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { BusinessOutline } from "@vicons/ionicons5";

// 消息提示
const message = useMessage();

// 图标
const BusinessOutlineIcon = BusinessOutline;

// 状态变量
const loading = ref(false);

// 业务机构选择器相关状态
const showOrgSelector = ref(false);

// 筛选表单
const filterForm = reactive({
  dateRange: "thisMonth", // 使用驼峰命名，在请求时会转换为下划线命名
  customDateRange: null,
  receivablePayableDateDimension: "days", // 应收应付趋势图的日期维度
  receptPaymentDateDimension: "days", // 收付款趋势图的日期维度
  selectedOrgs: [], // 选中的机构列表
});

// 统计数据
const statData = reactive({
  receivableConfirmed: {
    cnt: 0,
    amount: 0,
    yoyRate: 0, // 同比增长率（%）
    momRate: 0, // 环比增长率（%）
  },
  receivableUnConfirmed: {
    cnt: 0,
    amount: 0,
    yoyRate: 0,
    momRate: 0,
  },
  payableConfirmed: {
    cnt: 0,
    amount: 0,
    yoyRate: 0,
    momRate: 0,
  },
  payableUnConfirmed: {
    cnt: 0,
    amount: 0,
    yoyRate: 0,
    momRate: 0,
  },
  recept: {
    cnt: 0,
    amount: 0,
    yoyRate: 0,
    momRate: 0,
  },
  payment: {
    cnt: 0,
    amount: 0,
    yoyRate: 0,
    momRate: 0,
  },
});

// 图表引用
const orgIncomeExpenseChart = ref(null);
const orgReceivablePayableChart = ref(null);
const receivablePayableTrendChart = ref(null);
const receptPaymentTrendChart = ref(null);

// 图表实例
let orgIncomeExpenseChartInstance = null;
let orgReceivablePayableChartInstance = null;
let receivablePayableTrendChartInstance = null;
let receptPaymentTrendChartInstance = null;

// 格式化金额显示（分转元，添加千分位分隔符）
const formatAmount = (amount) => {
  // 直接使用formatMoney函数，它已经能处理null、undefined和非数字值
  // 将分转换为元并格式化
  return formatMoney(amount ? amount / 100 : 0);
};

// 格式化金额为万元单位显示（分转万元）
const formatAmountToWan = (amount) => {
  // 确保amount是数字类型
  const numAmount = Number(amount || 0);
  // 将分转换为万元并格式化，保留2位小数
  return (numAmount / 100 / 10000).toFixed(2) + "万";
};

// 格式化Y轴金额显示（分转元，转换为万元单位）
const formatYAxisAmount = (value) => {
  // 确保value是数字类型
  const numValue = Number(value || 0);
  // 先将分转换为元，再转换为万元单位显示
  return (numValue / 100 / 10000).toFixed(2) + "万";
};

// 格式化涨幅数据
const formatRate = (rate) => {
  if (rate === undefined || rate === null) return "0%";
  return (rate > 0 ? "+" : "") + rate.toFixed(1) + "%";
};

// 获取涨幅颜色
const getRateColor = (rate) => {
  if (rate === 0) return "#909399"; // 灰色
  return rate > 0 ? "#d03050" : "#18a058"; // 正值红色，负值绿色
};

// 日期维度选项
const dateDimensionOptions = [
  { label: "日", value: "days" },
  { label: "周", value: "weekdays" },
  { label: "月", value: "months" },
  { label: "年", value: "years" },
];

// 计算属性：选中机构的显示文本
const selectedOrgText = computed(() => {
  if (filterForm.selectedOrgs && filterForm.selectedOrgs.length > 0) {
    // 单选模式，只显示第一个选中的机构名称
    return filterForm.selectedOrgs[0].orgName;
  }
  return "选择统计机构";
});

// 根据日期范围设置默认日期维度
const setDefaultDateDimension = () => {
  // 根据日期范围设置应收应付趋势图的日期维度
  if (
    ["today", "yesterday", "thisWeek", "lastWeek"].includes(
      filterForm.dateRange
    )
  ) {
    filterForm.receivablePayableDateDimension = "days";
    filterForm.receptPaymentDateDimension = "days";
  } else if (["thisMonth", "lastMonth"].includes(filterForm.dateRange)) {
    filterForm.receivablePayableDateDimension = "days";
    filterForm.receptPaymentDateDimension = "days";
  } else if (["thisYear", "lastYear"].includes(filterForm.dateRange)) {
    filterForm.receivablePayableDateDimension = "months";
    filterForm.receptPaymentDateDimension = "months";
  }
};

// 处理应收应付趋势图日期维度变化
const handleReceivablePayableDateDimensionChange = (value) => {
  filterForm.receivablePayableDateDimension = value;
  fetchReceivablePayableTrend();
};

// 处理收付款趋势图日期维度变化
const handleReceptPaymentDateDimensionChange = (value) => {
  filterForm.receptPaymentDateDimension = value;
  fetchReceptPaymentTrend();
};

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  filterForm.dateRange = value;
  // 根据日期范围设置默认日期维度
  setDefaultDateDimension();
  handleDateChange(value, filterForm, fetchData);
};

// 处理自定义日期变化
const handleCustomDateChange = () => {
  fetchData();
};

// 处理机构选择
const handleOrgSelect = (orgs) => {
  if (orgs && orgs.length > 0) {
    // 单选模式，保存选中的机构
    filterForm.selectedOrgs = [...orgs];
    fetchData();
  }
};

// 处理机构选择取消
const handleOrgCancel = () => {
  showOrgSelector.value = false;
};

// 清空机构选择
const clearOrgSelection = () => {
  filterForm.selectedOrgs = [];
  fetchData();
};

// 获取查询参数
const getQueryParams = () => {
  const params = {};

  // 处理日期范围
  if (filterForm.dateRange) {
    // 将驼峰命名转换为下划线命名
    const dateScope =
      filterForm.dateRange === "custom"
        ? "custom"
        : filterForm.dateRange.replace(/([A-Z])/g, "_$1").toLowerCase();

    params.date_scope = dateScope;

    // 如果是自定义日期范围，还需要添加自定义日期参数
    if (dateScope === "custom" && filterForm.customDateRange) {
      const dateParams = getDateRangeParams(
        filterForm.dateRange,
        filterForm.customDateRange
      );
      if (dateParams.startDate) {
        params.start_date = dateParams.startDate;
      }
      if (dateParams.endDate) {
        params.end_date = dateParams.endDate;
      }
    }
  } else {
    // 默认使用本月
    params.date_scope = "this_month";
  }

  // 处理机构（单选模式）
  if (filterForm.selectedOrgs && filterForm.selectedOrgs.length > 0) {
    params.orgId = filterForm.selectedOrgs[0].id;
  }

  return params;
};

// 获取统计数据
const fetchStatData = async () => {
  try {
    loading.value = true;
    const params = getQueryParams();
    const response = await financialApi.getFinancialStats(params);

    if (response.code === 200) {
      // 更新统计数据，确保数值类型正确
      const data = response.data || {};

      // 处理应收账款数据
      if (data.receivableConfirmed) {
        statData.receivableConfirmed.cnt = Number(
          data.receivableConfirmed.cnt || 0
        );
        statData.receivableConfirmed.amount = Number(
          data.receivableConfirmed.amount || 0
        );
        statData.receivableConfirmed.yoyRate = Number(
          data.receivableConfirmed.yoyRate || 0
        );
        statData.receivableConfirmed.momRate = Number(
          data.receivableConfirmed.momRate || 0
        );
      }

      if (data.receivableUnConfirmed) {
        statData.receivableUnConfirmed.cnt = Number(
          data.receivableUnConfirmed.cnt || 0
        );
        statData.receivableUnConfirmed.amount = Number(
          data.receivableUnConfirmed.amount || 0
        );
        statData.receivableUnConfirmed.yoyRate = Number(
          data.receivableUnConfirmed.yoyRate || 0
        );
        statData.receivableUnConfirmed.momRate = Number(
          data.receivableUnConfirmed.momRate || 0
        );
      }

      // 处理应付账款数据
      if (data.payableConfirmed) {
        statData.payableConfirmed.cnt = Number(data.payableConfirmed.cnt || 0);
        statData.payableConfirmed.amount = Number(
          data.payableConfirmed.amount || 0
        );
        statData.payableConfirmed.yoyRate = Number(
          data.payableConfirmed.yoyRate || 0
        );
        statData.payableConfirmed.momRate = Number(
          data.payableConfirmed.momRate || 0
        );
      }

      if (data.payableUnConfirmed) {
        statData.payableUnConfirmed.cnt = Number(
          data.payableUnConfirmed.cnt || 0
        );
        statData.payableUnConfirmed.amount = Number(
          data.payableUnConfirmed.amount || 0
        );
        statData.payableUnConfirmed.yoyRate = Number(
          data.payableUnConfirmed.yoyRate || 0
        );
        statData.payableUnConfirmed.momRate = Number(
          data.payableUnConfirmed.momRate || 0
        );
      }

      // 处理已收账款数据
      if (data.recept) {
        statData.recept.cnt = Number(data.recept.cnt || 0);
        statData.recept.amount = Number(data.recept.amount || 0);
        statData.recept.yoyRate = Number(data.recept.yoyRate || 0);
        statData.recept.momRate = Number(data.recept.momRate || 0);
      }

      // 处理已付账款数据
      if (data.payment) {
        statData.payment.cnt = Number(data.payment.cnt || 0);
        statData.payment.amount = Number(data.payment.amount || 0);
        statData.payment.yoyRate = Number(data.payment.yoyRate || 0);
        statData.payment.momRate = Number(data.payment.momRate || 0);
      }
    } else {
      message.error(response.message || "获取财务统计数据失败");
    }
  } catch (error) {
    console.error("获取财务统计数据失败:", error);
    message.error("获取财务统计数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 获取机构维度收支总额数据并渲染图表
const fetchOrgIncomeExpenseData = async () => {
  try {
    const params = getQueryParams();
    const response = await financialApi.getOrgIncomeExpenseData(params);

    if (response.code === 200) {
      renderOrgIncomeExpenseChart(response.data);
    } else {
      message.error(response.message || "获取机构收支总额数据失败");
    }
  } catch (error) {
    console.error("获取机构收支总额数据失败:", error);
    message.error("获取机构收支总额数据失败，请稍后重试");
  }
};

// 获取机构维度应收应付数据并渲染图表
const fetchOrgReceivablePayableData = async () => {
  try {
    const params = getQueryParams();
    const response = await financialApi.getOrgReceivablePayableData(params);

    if (response.code === 200) {
      renderOrgReceivablePayableChart(response.data);
    } else {
      message.error(response.message || "获取机构应收应付数据失败");
    }
  } catch (error) {
    console.error("获取机构应收应付数据失败:", error);
    message.error("获取机构应收应付数据失败，请稍后重试");
  }
};

// 获取应收应付趋势数据并渲染图表
const fetchReceivablePayableTrend = async () => {
  try {
    const params = getQueryParams();
    // 添加日期维度参数
    params.date_dimension = filterForm.receivablePayableDateDimension;

    const response = await financialApi.getReceivablePayableTrend(params);

    if (response.code === 200) {
      renderReceivablePayableTrendChart(response.data);
    } else {
      message.error(response.message || "获取应收应付趋势数据失败");
    }
  } catch (error) {
    console.error("获取应收应付趋势数据失败:", error);
    message.error("获取应收应付趋势数据失败，请稍后重试");
  }
};

// 获取收款付款趋势数据并渲染图表
const fetchReceptPaymentTrend = async () => {
  try {
    const params = getQueryParams();
    // 添加日期维度参数
    params.date_dimension = filterForm.receptPaymentDateDimension;

    const response = await financialApi.getReceptPaymentTrend(params);

    if (response.code === 200) {
      renderReceptPaymentTrendChart(response.data);
    } else {
      message.error(response.message || "获取收款付款趋势数据失败");
    }
  } catch (error) {
    console.error("获取收款付款趋势数据失败:", error);
    message.error("获取收款付款趋势数据失败，请稍后重试");
  }
};

// 获取所有数据
const fetchData = () => {
  fetchStatData();
  fetchOrgIncomeExpenseData();
  fetchOrgReceivablePayableData();
  fetchReceivablePayableTrend();
  fetchReceptPaymentTrend();
};

// 渲染机构维度收支总额柱状图
const renderOrgIncomeExpenseChart = (data) => {
  if (!orgIncomeExpenseChartInstance) return;

  // 如果没有数据，使用空数据
  const chartData = data || {
    orgs: [],
    income: [],
    expense: [],
  };

  orgIncomeExpenseChartInstance.setOption({
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        let result = params[0].name + "<br/>";
        params.forEach((item) => {
          // 确保value是数字类型
          const value =
            typeof item.value === "number" ? item.value : Number(item.value);
          result += item.seriesName + ": " + formatAmount(value) + "<br/>";
        });
        return result;
      },
    },
    legend: {
      data: ["收入", "支出"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.orgs,
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: formatYAxisAmount,
      },
    },
    series: [
      {
        name: "收入",
        type: "bar",
        data: chartData.income,
        itemStyle: {
          color: "#2080f0",
        },
      },
      {
        name: "支出",
        type: "bar",
        data: chartData.expense,
        itemStyle: {
          color: "#f0a020",
        },
      },
    ],
  });
};

// 渲染机构维度应收应付柱状图
const renderOrgReceivablePayableChart = (data) => {
  if (!orgReceivablePayableChartInstance) return;

  // 如果没有数据，使用空数据
  const chartData = data || {
    orgs: [],
    receivables: [],
    payables: [],
  };

  orgReceivablePayableChartInstance.setOption({
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        let result = params[0].name + "<br/>";
        params.forEach((item) => {
          // 确保value是数字类型
          const value =
            typeof item.value === "number" ? item.value : Number(item.value);
          result += item.seriesName + ": " + formatAmount(value) + "<br/>";
        });
        return result;
      },
    },
    legend: {
      data: ["应收", "应付"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.orgs,
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: formatYAxisAmount,
      },
    },
    series: [
      {
        name: "应收",
        type: "bar",
        data: chartData.receivables,
        itemStyle: {
          color: "#18a058",
        },
      },
      {
        name: "应付",
        type: "bar",
        data: chartData.payables,
        itemStyle: {
          color: "#d03050",
        },
      },
    ],
  });
};

// 渲染应收应付趋势图
const renderReceivablePayableTrendChart = (data) => {
  if (!receivablePayableTrendChartInstance) return;

  // 如果没有数据，使用空数据
  const chartData = data || {
    dates: [],
    receivables: [],
    payables: [],
  };

  receivablePayableTrendChartInstance.setOption({
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let result = params[0].name + "<br/>";
        params.forEach((item) => {
          // 确保value是数字类型
          const value =
            typeof item.value === "number" ? item.value : Number(item.value);
          result += item.seriesName + ": " + formatAmount(value) + "<br/>";
        });
        return result;
      },
    },
    legend: {
      data: ["应收", "应付"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: chartData.dates,
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: formatYAxisAmount,
      },
    },
    series: [
      {
        name: "应收",
        type: "line",
        data: chartData.receivables,
        itemStyle: {
          color: "#18a058",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(24, 160, 88, 0.5)" },
            { offset: 1, color: "rgba(24, 160, 88, 0.1)" },
          ]),
        },
      },
      {
        name: "应付",
        type: "line",
        data: chartData.payables,
        itemStyle: {
          color: "#d03050",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(208, 48, 80, 0.5)" },
            { offset: 1, color: "rgba(208, 48, 80, 0.1)" },
          ]),
        },
      },
    ],
  });
};

// 渲染收款付款趋势图
const renderReceptPaymentTrendChart = (data) => {
  if (!receptPaymentTrendChartInstance) return;

  // 如果没有数据，使用空数据
  const chartData = data || {
    dates: [],
    recepts: [],
    payments: [],
  };

  receptPaymentTrendChartInstance.setOption({
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let result = params[0].name + "<br/>";
        params.forEach((item) => {
          // 确保value是数字类型
          const value =
            typeof item.value === "number" ? item.value : Number(item.value);
          result += item.seriesName + ": " + formatAmount(value) + "<br/>";
        });
        return result;
      },
    },
    legend: {
      data: ["收款", "付款"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: chartData.dates,
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: formatYAxisAmount,
      },
    },
    series: [
      {
        name: "收款",
        type: "line",
        data: chartData.recepts,
        itemStyle: {
          color: "#2080f0",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(32, 128, 240, 0.5)" },
            { offset: 1, color: "rgba(32, 128, 240, 0.1)" },
          ]),
        },
      },
      {
        name: "付款",
        type: "line",
        data: chartData.payments,
        itemStyle: {
          color: "#f0a020",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(240, 160, 32, 0.5)" },
            { offset: 1, color: "rgba(240, 160, 32, 0.1)" },
          ]),
        },
      },
    ],
  });
};

// 初始化所有图表
const initCharts = () => {
  // 初始化机构维度收支总额图表
  if (orgIncomeExpenseChart.value) {
    orgIncomeExpenseChartInstance = echarts.init(orgIncomeExpenseChart.value);
  }

  // 初始化机构维度应收应付图表
  if (orgReceivablePayableChart.value) {
    orgReceivablePayableChartInstance = echarts.init(
      orgReceivablePayableChart.value
    );
  }

  // 初始化应收应付趋势图表
  if (receivablePayableTrendChart.value) {
    receivablePayableTrendChartInstance = echarts.init(
      receivablePayableTrendChart.value
    );
  }

  // 初始化收款付款趋势图表
  if (receptPaymentTrendChart.value) {
    receptPaymentTrendChartInstance = echarts.init(
      receptPaymentTrendChart.value
    );
  }

  // 窗口大小变化时，重新调整图表大小
  window.addEventListener("resize", handleResize);
};

// 处理窗口大小变化
const handleResize = () => {
  orgIncomeExpenseChartInstance && orgIncomeExpenseChartInstance.resize();
  orgReceivablePayableChartInstance &&
    orgReceivablePayableChartInstance.resize();
  receivablePayableTrendChartInstance &&
    receivablePayableTrendChartInstance.resize();
  receptPaymentTrendChartInstance && receptPaymentTrendChartInstance.resize();
};

// 销毁图表实例
const destroyCharts = () => {
  window.removeEventListener("resize", handleResize);

  orgIncomeExpenseChartInstance && orgIncomeExpenseChartInstance.dispose();
  orgReceivablePayableChartInstance &&
    orgReceivablePayableChartInstance.dispose();
  receivablePayableTrendChartInstance &&
    receivablePayableTrendChartInstance.dispose();
  receptPaymentTrendChartInstance && receptPaymentTrendChartInstance.dispose();

  orgIncomeExpenseChartInstance = null;
  orgReceivablePayableChartInstance = null;
  receivablePayableTrendChartInstance = null;
  receptPaymentTrendChartInstance = null;
};

// 组件挂载时
onMounted(() => {
  // 初始化图表
  initCharts();

  // 根据日期范围设置默认日期维度
  setDefaultDateDimension();

  // 获取数据
  fetchData();
});

// 组件卸载前
onBeforeUnmount(() => {
  // 销毁图表实例
  destroyCharts();
});
</script>

<style scoped>
.financial-dashboard {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-card {
  margin-bottom: 16px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
}

.filter-label {
  width: 80px;
  font-weight: bold;
  color: #333;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.custom-radio-group {
  display: flex;
  flex-wrap: nowrap; /* 防止换行 */
  gap: 4px;
}

.custom-radio-button {
  margin-right: 4px;
  margin-bottom: 0;
  white-space: nowrap; /* 防止按钮文字换行 */
}

.custom-date-picker {
  width: 240px;
  min-width: 200px;
  flex-shrink: 1; /* 允许收缩 */
}

.stat-cards {
  margin-bottom: 16px;
}

.chart-container {
  margin-bottom: 16px;
}

.chart {
  height: 300px;
  width: 100%;
}

.amount-statistic {
  position: relative;
}

.amount-in-wan {
  display: inline-block;
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.rate-indicators {
  position: absolute;
  right: 8px;
  bottom: 2px;
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.rate-indicator {
  display: flex;
  align-items: center;
}

.rate-label {
  margin-right: 4px;
  color: #606266;
}

/* 自定义卡片标题样式 */
.custom-card :deep(.n-card-header) {
  position: relative;
  padding-left: 24px; /* 增加左侧内边距，为绿色方块留出更多空间 */
}

.custom-card :deep(.n-card-header .n-card-header__main) {
  font-size: 16px;
  font-weight: bold;
  color: #18a058; /* 主题色 - 绿色 */
}

.custom-card :deep(.n-card-header)::before {
  content: "";
  position: absolute;
  left: 8px; /* 调整左侧间距，使方块不紧贴边框 */
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 16px;
  background-color: #18a058; /* 绿色小方块 */
  border-radius: 2px;
}

/* 机构选择器容器样式 */
.org-selector-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-orgs-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 响应式样式 - 针对小屏幕优化 */
@media (max-width: 1366px) {
  .filter-options {
    gap: 8px;
  }

  .custom-radio-group {
    gap: 2px;
  }

  .custom-radio-button {
    margin-right: 2px;
  }

  .custom-date-picker {
    width: 200px;
    min-width: 180px;
  }
}

@media (max-width: 1280px) {
  .custom-date-picker {
    width: 180px;
    min-width: 160px;
  }

  .filter-options {
    gap: 6px;
  }
}
</style>
